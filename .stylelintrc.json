{"extends": ["stylelint-config-standard-scss"], "plugins": ["stylelint-scss"], "rules": {"scss/at-rule-no-unknown": true, "selector-class-pattern": null, "keyframes-name-pattern": null, "custom-property-no-missing-var-function": true, "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["open"]}], "selector-type-no-unknown": [true, {"ignoreTypes": ["custom"]}], "media-feature-range-notation": null, "value-keyword-case": "lower", "function-no-unknown": [true, {"ignoreFunctions": ["running"]}]}}