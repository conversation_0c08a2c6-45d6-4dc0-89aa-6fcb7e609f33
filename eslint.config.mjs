import react from 'eslint-plugin-react';
import typescriptEslint from '@typescript-eslint/eslint-plugin';
import globals from 'globals';
import tsParser from '@typescript-eslint/parser';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import js from '@eslint/js';
import { FlatCompat } from '@eslint/eslintrc';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

export default [{
  ignores: ['**/dist', '**/node_modules'],
}, ...compat.extends('eslint:recommended', 'plugin:@typescript-eslint/recommended', 'airbnb'), {
  plugins: {
    react,
    '@typescript-eslint': typescriptEslint,
  },

  languageOptions: {
    globals: {
      ...globals.browser,
      ...globals.node,
      ...globals.jest,
      window: true,
      document: true,
      localStorage: true,
      FormData: true,
      FileReader: true,
      Blob: true,
      API_URL: 'readonly',
      navigator: true,
      JSX: true,
      AudioWorkletGlobalScope: 'readonly',
    },

    parser: tsParser,
    ecmaVersion: 6,
    sourceType: 'module',

    parserOptions: {
      ecmaFeatures: {
        jsx: true,
      },
    },
  },

  rules: {
    'default-param-last': 0,
    'import/extensions': 0,
    'import/no-named-as-default': 0,
    'function-paren-newline': 0,
    'function-call-argument-newline': 0,
    'linebreak-style': 0,
    'jsx-a11y/label-has-for': 'off',
    'jsx-a11y/label-has-associated-control': 'off',
    'react/require-default-props': 'off',

    'class-methods-use-this': ['error', {
      exceptMethods: [
        'render',
        'getInitialState',
        'getDefaultProps',
        'getChildContext',
        'componentWillMount',
        'componentDidMount',
        'componentWillReceiveProps',
        'shouldComponentUpdate',
        'componentWillUpdate',
        'componentDidUpdate',
        'componentWillUnmount',
        'componentDidCatch',
      ],
    }],

    'react/function-component-definition': [1, {
      namedComponents: 'arrow-function',
      unnamedComponents: 'function-expression',
    }],

    'react/no-unstable-nested-components': 0,

    'react/jsx-filename-extension': [1, {
      extensions: ['.js', '.jsx', '.tsx'],
    }],

    'react/jsx-uses-react': 'error',
    'react/jsx-uses-vars': 'error',
    'no-mixed-operators': 1,
    'no-unused-vars': 'off',
    '@typescript-eslint/no-unused-vars': 'error',
    'no-underscore-dangle': 0,
    'import/imports-first': ['error', 'absolute-first'],
    'import/newline-after-import': 'error',
    'import/no-unresolved': 'off',

    'import/no-extraneous-dependencies': ['error', {
      devDependencies: true,
    }],

    'react/jsx-props-no-spreading': 'off',

    'max-len': ['warn', {
      code: 120,
      ignoreComments: true,
      ignoreUrls: true,
      ignoreStrings: true,
      ignoreTemplateLiterals: true,
      ignoreRegExpLiterals: true,
    }],
  },
}, {
  files: ['**/*.ts, **/*.tsx'],

  languageOptions: {
    ecmaVersion: 5,
    sourceType: 'script',

    parserOptions: {
      project: 'tsconfig.json',
      impliedStrict: true,
      createDefaultProgram: false,
    },
  },
}, {
  files: ['**/*.spec.ts', '**/*.spec.tsx'],

  languageOptions: {
    ecmaVersion: 5,
    sourceType: 'script',

    parserOptions: {
      project: 'tsconfig.eslint.json',
      impliedStrict: true,
      createDefaultProgram: false,
    },
  },
}];
