import React, { ReactElement } from 'react';
import { render } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import theme from '@nv2/nv2-pkg-js-theme/src/components/theme/theme';
import themeConfig from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';
import { MemoryRouter } from 'react-router-dom';

const themeName = 'nextgen';
const currentTheme = themeConfig[themeName];

interface ITestProviderProps {
  children: ReactElement | ReactElement[];
  route: string | undefined;
}

const TestProvider = ({ children, route }: ITestProviderProps) => {
  const routerProps = {
    ...(route && { initialEntries: [route] }),
  };

  return (
    <ThemeProvider theme={theme(currentTheme)}>
      <MemoryRouter {...routerProps}>
        {children}
      </MemoryRouter>
    </ThemeProvider>
  );
};

export default function testRender(ui: ReactElement | ReactElement[], route?: string) {
  return render(<TestProvider route={route}>{ui}</TestProvider>);
}
