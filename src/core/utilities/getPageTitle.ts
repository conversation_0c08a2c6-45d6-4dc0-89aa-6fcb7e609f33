const getPageTitle = (urlParams: string | undefined): string => {
  if (urlParams) {
    let title = 'IOT Portal - ';

    if (urlParams.includes('accounts')) {
      title += 'Accounts';
    }

    if (urlParams.includes('sim-management')) {
      title += 'Sim Management';
    }

    if (urlParams.includes('rate-plans')) {
      title += 'Rate Plans';
    }

    if (urlParams.includes('billing')) {
      title += 'Billing';
    }

    if (urlParams.includes('automation-rules')) {
      title += 'Automation Rules';
    }

    return title;
  }

  return 'IOT Portal - Container';
};

export default getPageTitle;
