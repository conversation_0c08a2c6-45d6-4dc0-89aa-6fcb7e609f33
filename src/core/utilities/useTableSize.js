import { useEffect, useState, useCallback } from 'react';

const useTableSize = (tableContainer) => {
  const [maxBodyHeight, setMaxBodyHeight] = useState(0);

  const handleResize = useCallback(() => {
    const table = document.querySelector('.simple-table');
    const tableMob = document.querySelector('.simple-table-mob');

    const tableTop = table ? table.getBoundingClientRect().top
      : tableMob.getBoundingClientRect().top;
    setMaxBodyHeight(tableContainer.clientHeight - tableTop);
  }, [tableContainer]);

  useEffect(() => {
    if (tableContainer) {
      handleResize();
      window.addEventListener('resize', handleResize);
    }
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [tableContainer, handleResize]);

  return { maxBodyHeight };
};

export default useTableSize;
