import { Cookies } from 'react-cookie';
import { setRequestErrorInterceptor, setRequestHeadersInterceptor } from './requestInterceptors';

describe('Request Interceptors', () => {
  test('should set headers configuration', () => {
    const fakeConfig = { headers: {} };
    const fakeAccessToken = '1';
    const fakeCookies = new Cookies(`nv2-auth-access-token="${fakeAccessToken}"`).cookies;
    const expectedHeadersConfig = {
      Authorization: `Bearer ${fakeAccessToken}`,
      'Cache-control': 'no-cache',
    };

    const receivedHeaderConfig = setRequestHeadersInterceptor(fakeConfig, fakeCookies);

    expect(receivedHeaderConfig.headers).toEqual(expectedHeadersConfig);
  });

  test('should call passed callback if error status = 401', async () => {
    const unAuthorizedCodeStatus = 401;
    const mockRedirectToLogout = jest.fn();
    const fakeError = {
      response: {
        status: unAuthorizedCodeStatus,
      },
    };

    await setRequestErrorInterceptor(fakeError, mockRedirectToLogout);

    expect(mockRedirectToLogout).toBeCalled();
  });
});
