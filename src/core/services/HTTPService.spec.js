import axios from 'axios';
import * as interceptors from 'core/interceptors/requestInterceptors';
import MockAdapter from 'axios-mock-adapter';
import { HTTPService } from './HTTPService';

describe('HTTPService', () => {
  const fakeApiUrl = 'https://test-api';
  const fakeAxios = axios.create();

  test('should set apiUrl and "withCredentials = true" to default axios instance configuration', () => {
    HTTPService.setDefaultGlobalConfig(fakeAxios, fakeApiUrl);

    expect(fakeAxios.defaults.baseURL).toBe(fakeApiUrl);
    expect(fakeAxios.defaults.withCredentials).toBe(true);
  });

  test('should set config to request headers if axios instance is configured with "setRequestHeadersInterceptor"', async () => {
    const fakeConfig = {
      headers: {
        Authorization: 'Bearer testAccessToken',
      },
    };

    jest.spyOn(interceptors, 'setRequestHeadersInterceptor').mockImplementation((prevConfig) => ({
      ...prevConfig,
      ...fakeConfig,
    }));

    HTTPService.setAccessToken(fakeAxios);

    const mockAxios = new MockAdapter(fakeAxios);

    mockAxios.onGet().reply(200, {});

    const { config } = await fakeAxios.get();

    expect(config.headers.Authorization).toEqual(fakeConfig.headers.Authorization);
  });

  test('should call callback in "setRequestErrorInterceptor" if axios instance is configured with it', async () => {
    const fakeLogout = jest.fn();

    jest.spyOn(interceptors, 'setRequestErrorInterceptor').mockImplementation(fakeLogout);

    HTTPService.setCorsError(fakeAxios);

    const mockAxios = new MockAdapter(fakeAxios);

    mockAxios.onGet().reply(500, {});

    try {
      await fakeAxios.get();
      expect(fakeLogout).toBeCalled();
    } catch {
      // Intentionally ignored
    }
  });

  test('should return AbortController instance', async () => {
    expect(HTTPService.getController() instanceof AbortController).toBeTruthy();
  });

  test('should abort controller', async () => {
    const abortSpy = jest.spyOn(AbortController.prototype, 'abort');
    const testController = new AbortController();

    HTTPService.cancelRequest(testController);

    expect(abortSpy).toBeCalledTimes(1);
  });
});
