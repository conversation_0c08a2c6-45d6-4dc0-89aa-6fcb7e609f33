import CookiesService from './CookiesService';

describe('CookiesService', () => {
  const mockCookies = {
    'nv2-auth-access-token': 'test-access-token',
    'nv2-auth-user-email': '<EMAIL>',
    'nv2-auth-user-name': 'Test User',
  };

  it('should return the access token from cookies', () => {
    const result = CookiesService.getAccessToken(mockCookies);
    expect(result).toEqual({ value: 'test-access-token' });
  });

  it('should return the user name from cookies', () => {
    const result = CookiesService.getUserName(mockCookies);
    expect(result).toEqual({ value: 'Test User' });
  });

  it('should return the user email from cookies', () => {
    const result = CookiesService.getUserEmail(mockCookies);
    expect(result).toEqual({ value: '<EMAIL>' });
  });

  it('should return undefined for access token if cookies are not provided', () => {
    const result = CookiesService.getAccessToken(null);
    expect(result).toEqual({ value: null });
  });

  it('should return undefined for user name if cookies are not provided', () => {
    const result = CookiesService.getUserName(null);
    expect(result).toEqual({ value: null });
  });

  it('should return undefined for user email if cookies are not provided', () => {
    const result = CookiesService.getUserEmail(null);
    expect(result).toEqual({ value: null });
  });
});
