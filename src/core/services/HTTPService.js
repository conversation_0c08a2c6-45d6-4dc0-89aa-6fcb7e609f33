import axios from 'axios';

import {
  setRequestHeadersInterceptor,
  setRequestErrorInterceptor,
} from 'core/interceptors';

const coreAxios = axios.create();

class HTTPService {
  static setDefaultGlobalConfig(axiosInstance, apiUrl) {
    /* eslint-disable-next-line no-param-reassign */
    axiosInstance.defaults.baseURL = apiUrl;
    /* eslint-disable-next-line no-param-reassign */
    axiosInstance.defaults.withCredentials = true;
  }

  static setAccessToken(axiosInstance, cookies) {
    axiosInstance.interceptors.request
      .use((prevConfig) => setRequestHeadersInterceptor(prevConfig, cookies));
  }

  static setCorsError(axiosInstance, redirectToLogout, logoutUrl) {
    axiosInstance.interceptors.response.use((res) => res, (error) => {
      setRequestErrorInterceptor(error, redirectToLogout, logoutUrl);
      return Promise.reject(error);
    });
  }

  static getController() {
    return new AbortController();
  }

  static cancelRequest(controller) {
    controller.abort();
  }
}

export {
  HTTPService,
  coreAxios,
};
