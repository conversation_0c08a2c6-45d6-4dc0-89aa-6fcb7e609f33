const nv2AuthCookieName = {
  accessToken: 'nv2-auth-access-token',
  userEmail: 'nv2-auth-user-email',
  userName: 'nv2-auth-user-name',
};

export default class CookiesService {
  static getAccessToken(cookies) {
    return {
      value: cookies && cookies[nv2AuthCookieName.accessToken],
    };
  }

  static getUserName(cookies) {
    return {
      value: cookies && cookies[nv2AuthCookieName.userName],
    };
  }

  static getUserEmail(cookies) {
    return {
      value: cookies && cookies[nv2AuthCookieName.userEmail],
    };
  }
}
