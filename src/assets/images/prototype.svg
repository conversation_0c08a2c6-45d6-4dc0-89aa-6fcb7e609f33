<svg width="389" height="148" viewBox="0 0 389 148" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_751_2231)">
<path d="M8 63.2609C8 62.0122 9.01223 61 10.2609 61H108.739C109.988 61 111 62.0122 111 63.2609V73.766V133.739C111 134.988 109.988 136 108.739 136H10.2609C9.01223 136 8 134.988 8 133.739V73.766V63.2609Z" fill="white"/>
</g>
<path d="M8 74H111" stroke="#F3F3F3" stroke-dasharray="2 2"/>
<g filter="url(#filter1_d_751_2231)">
<path d="M279.538 39H378.462C379.863 39 381 40.1365 381 41.5385V54.8V91.8312V115.462C381 116.863 379.863 118 378.462 118H279.538C278.137 118 277 116.863 277 115.462V54.8V41.5385C277 40.1365 278.137 39 279.538 39Z" fill="white"/>
</g>
<g filter="url(#filter2_d_751_2231)">
<path d="M147.261 4H239.739C240.988 4 242 5.01223 242 6.26087V16.766V76.7391C242 77.9878 240.988 79 239.739 79H191.897H147.261C146.012 79 145 77.9878 145 76.7391V49.4787V16.766V6.26087C145 5.01223 146.012 4 147.261 4Z" fill="white"/>
</g>
<mask id="path-5-inside-1_751_2231" fill="white">
<rect x="19" y="88" width="28" height="4" rx="0.753623"/>
</mask>
<rect x="19" y="88" width="28" height="4" rx="0.753623" fill="#DFDEF4" stroke="#DFDEF4" stroke-width="4" mask="url(#path-5-inside-1_751_2231)"/>
<mask id="path-6-inside-2_751_2231" fill="white">
<rect x="157" y="30" width="28" height="4" rx="1"/>
</mask>
<rect x="157" y="30" width="28" height="4" rx="1" fill="black" stroke="#DFDEF4" stroke-width="4" mask="url(#path-6-inside-2_751_2231)"/>
<rect x="289" y="99" width="15" height="4" rx="1" transform="rotate(-90 289 99)" fill="#C9C7ED"/>
<mask id="path-8-inside-3_751_2231" fill="white">
<rect x="289" y="103" width="79" height="5" rx="1"/>
</mask>
<rect x="289" y="103" width="79" height="5" rx="1" fill="black" stroke="#DFDEF4" stroke-width="5" mask="url(#path-8-inside-3_751_2231)"/>
<mask id="path-9-inside-4_751_2231" fill="white">
<rect x="283" y="64" width="14" height="4" rx="0.846152"/>
</mask>
<rect x="283" y="64" width="14" height="4" rx="0.846152" fill="black" stroke="#DFDEF4" stroke-width="4" mask="url(#path-9-inside-4_751_2231)"/>
<rect x="299" y="99" width="7" height="4" rx="1" transform="rotate(-90 299 99)" fill="#C9C7ED"/>
<rect x="310" y="99" width="25" height="4" rx="1" transform="rotate(-90 310 99)" fill="#C9C7ED"/>
<rect x="320" y="99" width="20" height="4" rx="1" transform="rotate(-90 320 99)" fill="#C9C7ED"/>
<rect x="331" y="99" width="17" height="4" rx="1" transform="rotate(-90 331 99)" fill="#C9C7ED"/>
<rect x="342" y="99" width="27" height="4" rx="1" transform="rotate(-90 342 99)" fill="#9B99DE"/>
<rect x="352" y="99" width="35" height="4" rx="1" transform="rotate(-90 352 99)" fill="#9B99DE"/>
<rect x="363" y="99" width="21" height="4" rx="1" transform="rotate(-90 363 99)" fill="#9B99DE"/>
<mask id="path-17-inside-5_751_2231" fill="white">
<rect x="19" y="97" width="35" height="4" rx="0.753623"/>
</mask>
<rect x="19" y="97" width="35" height="4" rx="0.753623" fill="#DFDEF4" stroke="#DFDEF4" stroke-width="4" mask="url(#path-17-inside-5_751_2231)"/>
<mask id="path-18-inside-6_751_2231" fill="white">
<rect x="157" y="39" width="34" height="4" rx="1"/>
</mask>
<rect x="157" y="39" width="34" height="4" rx="1" fill="black" stroke="#DFDEF4" stroke-width="4" mask="url(#path-18-inside-6_751_2231)"/>
<mask id="path-19-inside-7_751_2231" fill="white">
<rect x="157" y="48" width="23" height="4" rx="1"/>
</mask>
<rect x="157" y="48" width="23" height="4" rx="1" fill="black" stroke="#DFDEF4" stroke-width="4" mask="url(#path-19-inside-7_751_2231)"/>
<mask id="path-20-inside-8_751_2231" fill="white">
<rect x="157" y="61" width="14" height="5" rx="1"/>
</mask>
<rect x="157" y="61" width="14" height="5" rx="1" fill="black" stroke="#DFDEF4" stroke-width="5" mask="url(#path-20-inside-8_751_2231)"/>
<mask id="path-21-inside-9_751_2231" fill="white">
<rect x="19" y="106" width="29" height="4" rx="0.753623"/>
</mask>
<rect x="19" y="106" width="29" height="4" rx="0.753623" fill="black" stroke="#DFDEF4" stroke-width="4" mask="url(#path-21-inside-9_751_2231)"/>
<rect x="19" y="114" width="37" height="9" rx="0.753623" fill="#DFDEF4"/>
<circle cx="15" cy="68" r="2" fill="#E7E7EE"/>
<circle cx="285" cy="47" r="2" fill="#E7E7EE"/>
<circle cx="152" cy="11" r="2" fill="#E7E7EE"/>
<circle cx="22" cy="68" r="2" fill="#E7E7EE"/>
<circle cx="292" cy="47" r="2" fill="#E7E7EE"/>
<circle cx="159" cy="11" r="2" fill="#E7E7EE"/>
<circle cx="29" cy="68" r="2" fill="#E7E7EE"/>
<circle cx="299" cy="47" r="2" fill="#E7E7EE"/>
<circle cx="166" cy="11" r="2" fill="#E7E7EE"/>
<path d="M81 88C77.6377 88 74.3509 88.997 71.5553 90.865C68.7597 92.733 66.5807 95.388 65.2941 98.4944C64.0074 101.601 63.6707 105.019 64.3267 108.317C64.9826 111.614 66.6017 114.643 68.9792 117.021C71.3567 119.398 74.3858 121.017 77.6835 121.673C80.9811 122.329 84.3993 121.993 87.5056 120.706C90.612 119.419 93.267 117.24 95.135 114.445C97.003 111.649 98 108.362 98 105L81 105L81 88Z" fill="#C9C7ED"/>
<path d="M100 102C100 99.8989 99.5603 97.8183 98.706 95.8771C97.8516 93.9359 96.5994 92.172 95.0208 90.6863C93.4422 89.2006 91.5682 88.022 89.5056 87.2179C87.4431 86.4139 85.2325 86 83 86V102H100Z" fill="#9B99DE"/>
<path d="M220.767 31H203.754C203.337 31 203 31.3374 203 31.7536V65.2464C203 65.6626 203.337 66 203.754 66H228.246C228.663 66 229 65.6626 229 65.2464V38.5M220.767 31V37.7464C220.767 38.1626 221.104 38.5 221.52 38.5H229M220.767 31L229 38.5" stroke="#C9C7ED" stroke-width="3"/>
<path d="M220.767 37.7464V31.2264C220.767 31.1395 220.87 31.0939 220.934 31.1524L228.809 38.3261C228.877 38.3876 228.833 38.5 228.742 38.5H221.52C221.104 38.5 220.767 38.1626 220.767 37.7464Z" stroke="#9B99DE" stroke-width="3"/>
<rect x="222" y="34" width="3" height="3" fill="#9B99DE"/>
<circle cx="43.5" cy="89.5" r="12.5" fill="#FBEEC3" fill-opacity="0.34"/>
<circle cx="203.5" cy="36.5" r="12.5" fill="#FBEEC3" fill-opacity="0.34"/>
<circle cx="354.5" cy="71.5" r="12.5" fill="#FBEEC3" fill-opacity="0.34"/>
<path d="M44 89.4999C44 89.4999 58.0524 57.3534 106 34.4999C159.5 8.9999 198.5 33.4999 198.5 33.4999M198.5 33.4999L196 25.4999M198.5 33.4999L189.5 35.4999" stroke="#F3C73C" stroke-width="2" stroke-dasharray="3 3"/>
<path d="M196 25.5L198.5 33.5L189.5 35.5" stroke="#F3C73C" stroke-width="2"/>
<circle cx="43.5" cy="89.5" r="4.5" fill="#F3C73C"/>
<circle cx="203.5" cy="36.5" r="4.5" fill="#F3C73C"/>
<circle cx="354.5" cy="71.5" r="4.5" fill="#F3C73C"/>
<path d="M145 17H242" stroke="#F3F3F3" stroke-dasharray="2 2"/>
<path d="M277 54H381" stroke="#F3F3F3" stroke-dasharray="2 2"/>
<path d="M341.615 76.6457L350.79 76.2685L350.79 85.5641" stroke="#F3C73C" stroke-width="2"/>
<path d="M203.5 36.5C203.5 36.5 202.103 33.0001 246 81C289.897 129 350 76.5 350 76.5" stroke="#F3C73C" stroke-width="2" stroke-dasharray="3 3"/>
<defs>
<filter id="filter0_d_751_2231" x="0" y="57" width="119" height="91" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.176471 0 0 0 0 0.164706 0 0 0 0 0.505882 0 0 0 0.1 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_751_2231"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_751_2231" result="shape"/>
</filter>
<filter id="filter1_d_751_2231" x="269" y="35" width="120" height="95" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.176471 0 0 0 0 0.164706 0 0 0 0 0.505882 0 0 0 0.1 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_751_2231"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_751_2231" result="shape"/>
</filter>
<filter id="filter2_d_751_2231" x="137" y="0" width="113" height="91" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.176471 0 0 0 0 0.164706 0 0 0 0 0.505882 0 0 0 0.1 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_751_2231"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_751_2231" result="shape"/>
</filter>
</defs>
</svg>
