import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
import ErrorBoundry from 'components/ErrorBoundry';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import App from './App';
import { HtmlNode } from './App.types';

class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean }> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, info: React.ErrorInfo) {
    // Log the error to an error reporting service or console
    // eslint-disable-next-line no-console
    console.error('Error Boundary Caught an Error:', error, info);
  }

  render() {
    // eslint-disable-next-line react/destructuring-assignment
    if (this.state.hasError) {
      return (
        <ErrorBoundry
          getCurrentThemeColors={(color: string) => getBrandColors(color, 'bt')}
          primaryColor="#F200F5"
          secondaryColor="#5514B4"
        />
      );
    }
    // eslint-disable-next-line react/destructuring-assignment
    return this.props.children;
  }
}

const rootElement: HtmlNode = document.querySelector('#root');
if (rootElement) {
  const root = createRoot(rootElement);

  // Detect stale chunk errors and reload
  window.addEventListener('error', (event) => {
    if (event.message?.includes('Loading chunk')) {
      window.location.reload();
    }
  });

  window.addEventListener('unhandledrejection', (event) => {
    const reason = event?.reason?.message || '';
    if (reason.includes('Loading chunk') || reason.includes('ChunkLoadError')) {
      // eslint-disable-next-line no-console
      console.warn('[Chunk Promise Error] Reloading app...');
      window.location.reload();
    }
  });

  root.render(
    <BrowserRouter>
      <ErrorBoundary>
        <App />
      </ErrorBoundary>
    </BrowserRouter>,
  );
}
