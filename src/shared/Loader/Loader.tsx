import React from 'react';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import { Box, CircularProgress } from '@mui/material';

const Loader = () => (
  <Box style={{ display: 'flex', height: 'calc(100vh - 100px)' }}>
    <Box sx={{ position: 'relative', margin: 'auto' }}>
      <CircularProgress
        data-testid="loader"
        variant="determinate"
        sx={{
          color: styles.lightColor50,
        }}
        size={60}
        thickness={4}
        value={100}
      />
      <CircularProgress
        variant="indeterminate"
        disableShrink
        sx={{
          color: (theme) => theme.palette.secondary.main,
          animationDuration: '550ms',
          position: 'absolute',
          left: 0,
        }}
        size={60}
        thickness={4}
      />
    </Box>
  </Box>
);

export default Loader;
