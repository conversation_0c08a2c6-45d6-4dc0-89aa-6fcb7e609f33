import React from 'react';
import { render, screen } from '@testing-library/react';
import { AppContext } from 'AppContextProvider';
import Analytics from './Analytics';

const mockAppConfig = {
  analyticsUrl: 'https://analytics.dev.spogconnected.com',
};

const mockContextValue = {
  appConfig: mockAppConfig,
  user: undefined,
  isLoading: false,
  primaryColor: '#000',
  secondaryColor: '#000',
  themeName: 'test',
  getBrandColors: jest.fn(),
};

describe('Analytics Component', () => {
  it('should render iframe with analytics URL from config', () => {
    render(
      <AppContext.Provider value={mockContextValue}>
        <Analytics />
      </AppContext.Provider>,
    );

    const iframe = screen.getByTitle('Superset Analytics Panel');
    expect(iframe).toBeInTheDocument();
    expect(iframe).toHaveAttribute('src', 'https://analytics.dev.spogconnected.com');
  });
});
