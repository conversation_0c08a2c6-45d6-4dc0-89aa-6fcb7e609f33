import React from 'react';

import { Button, Typography } from '@mui/material';

import ErrorBoundryIcon from './ErrorBoundryIcon';

interface IErrorBoundryProps {
  primaryColor: string
  getCurrentThemeColors: (color: string) => { [key: string]: string }
  secondaryColor?: string | null
}

const ErrorBoundry = ({
  getCurrentThemeColors, primaryColor, secondaryColor,
}: IErrorBoundryProps) => {
  const getRedirectDomain = () => {
    const currentHost = window.location.hostname;

    switch (true) {
      case currentHost.includes('app.dev'):
        return process.env.REACT_APP_DEV_DOMAIN;
      case currentHost.includes('app.test'):
        return process.env.REACT_APP_TEST_DOMAIN;
      case currentHost.includes('app.stage'):
        return process.env.REACT_APP_STAGE_DOMAIN;
      case currentHost.includes('app.iotportal'):
        return process.env.REACT_APP_PROD_DOMAIN;
      default:
        return '/';
    }
  };

  const homepageHref = getRedirectDomain() ?? '/';

  return (
    <div className="ErrorBoundry__wrap">
      <div className="ErrorBoundry">
        <ErrorBoundryIcon
          primaryColor={primaryColor}
          getCurrentThemeColors={getCurrentThemeColors}
          secondaryColor={secondaryColor}
        />
        <Typography variant="h3" component="h4" className="ErrorBoundry__title">Oops! Something went wrong</Typography>
        <Typography variant="body1" component="p" className="ErrorBoundry__text">
          Try refreshing, or head back to the homepage.
        </Typography>
        <Button
          component="a"
          variant="contained"
          sx={{
            backgroundColor: secondaryColor || primaryColor,
          }}
          className="ErrorBoundry__btn"
          data-testid="ErrorBoundry__btn"
          href={homepageHref}
        >
          Back to Homepage
        </Button>
      </div>
    </div>
  );
};

ErrorBoundry.defaultProps = {
  secondaryColor: null,
};

export default ErrorBoundry;
