import React from 'react';
import { AiOutlineSearch } from 'react-icons/ai';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';

interface IErrorBoundryIconProps {
  primaryColor: string
  getCurrentThemeColors?: (color: string) => { [key: string]: string }
  secondaryColor?: string | null
}

const ErrorBoundryIcon = ({
  primaryColor, getCurrentThemeColors, secondaryColor,
}: IErrorBoundryIconProps) => {
  const primaryThemeColors = getCurrentThemeColors?.(primaryColor) || {};
  const secondaryThemeColors = getCurrentThemeColors?.(secondaryColor || primaryColor) || {};

  return (
    <div className="ErrorBoundry__icon-wrap">
      <svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M65.4748 35.9263C68.0009 29.3396 75.3883 26.0478 81.975 28.5739L136.074 49.3215C142.66 51.8476 145.952 59.235 143.426 65.8217L119.428 128.397L113.525 143.787C110.999 150.374 103.612 153.666 97.025 151.14L88.0235 147.688L42.9263 130.392C36.3396 127.866 33.0478 120.479 35.5739 113.892L65.4748 35.9263Z"
          fill={secondaryThemeColors[100]}
        />
        <path
          d="M49.9607 100.6C51.2238 97.3064 54.9175 95.6605 58.2108 96.9235L87.1296 108.014C90.423 109.277 92.0689 112.971 90.8058 116.264L86.7162 126.928L86.665 127.062C85.402 130.355 81.7083 132.001 78.4149 130.738L73.4123 128.819L49.4961 119.647C46.2027 118.384 44.5569 114.69 45.8199 111.397L49.9607 100.6Z"
          fill={secondaryThemeColors[200]}
        />
        <path
          d="M53 45.8778C53 38.8233 58.7188 33.1045 65.7733 33.1045H122.455C129.509 33.1045 135.228 38.8233 135.228 45.8778V110.796L124.899 124.607L113.152 139.282H65.7733C58.7188 139.282 53 133.564 53 126.509V45.8778Z"
          fill={primaryThemeColors[50]}
        />
        <path
          d="M112.521 139.242V111.269L134.874 111.269L112.521 139.242Z"
          fill={primaryThemeColors[100]}
        />
        <path d="M112.609 111.27V139.242H89.3699L112.609 111.27Z" fill={primaryThemeColors[200]} />
        <circle cx="89.5" cy="89.5" r="88.5" stroke={primaryThemeColors[200]} strokeDasharray="4 4" />
      </svg>
      <AiOutlineSearch
        size={34}
        color={primaryThemeColors[200]}
        className="ErrorBoundry__icon"
      />
    </div>
  );
};

ErrorBoundryIcon.defaultProps = {
  getCurrentThemeColors: (hex: string) => getBrandColors(hex),
  secondaryColor: null,
};
export default ErrorBoundryIcon;
