.ErrorBoundry {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: $white-color;

  &__wrap {
    height: calc(100vh - 60px);
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: $dark-color-500;
    justify-content: center;
    padding: 20px;

    @media (max-width: $mobile-width) {
      height: calc(100vh - 80px);
    }
  }

  &__icon {
    position: absolute;

    &-wrap {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 177px;
      height: 177px;
    }
  }

  &__error {
    font-size: 100px !important;
    font-weight: 700 !important;
    line-height: 136.18px !important;
  }

  &__title {
    margin-top: 26px !important;
    margin-bottom: 5px !important;
    font-size: 26px !important;
    font-weight: 700 !important;
    text-align: center;
  }

  &__text {
    margin-bottom: 30px !important;
    text-align: center !important;
    font-size: 14px !important;
    color: $dark-color-300;
  }
}
