<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" data-abs-path />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Web site" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" data-abs-path />
    <link
      rel="manifest"
      href="/manifest.json"
      crossorigin="use-credentials"
      data-abs-path
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <title>SPOG Container UI</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
  <script>
    let hasReloadedOnce = sessionStorage.getItem("hasReloadedOnError");

    function handleChunkError(message) {
      const keywords = [
        "Loading chunk",
        "ChunkLoadError",
        "Unexpected token",
        "Cannot read properties",
      ];
      return keywords.some((keyword) => message.includes(keyword));
    }

    window.addEventListener("error", function (event) {
      const message = event?.message || "";
      if (
        message.includes("Loading chunk") ||
        message.includes("ChunkLoadError")
      ) {
        console.warn("[Chunk Error] Reloading app...");
        window.location.reload();
      }
    });

    window.addEventListener("unhandledrejection", function (event) {
      const reason = event?.reason?.message || "";
      if (
        reason.includes("Loading chunk") ||
        reason.includes("ChunkLoadError")
      ) {
        console.warn("[Chunk Promise Error] Reloading app...");
        window.location.reload();
      }
    });
  </script>
</html>
